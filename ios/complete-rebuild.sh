#!/bin/bash

echo "Starting complete rebuild process..."

# Step 1: Clean everything
echo "Step 1: Cleaning everything..."
rm -rf ~/Library/Developer/Xcode/DerivedData/PSquare-*
rm -rf Pods
rm -rf build
rm -f Podfile.lock

# Step 2: Fix node_modules issues
echo "Step 2: Fixing node_modules issues..."
cd ..
rm -rf node_modules
npm cache clean --force
npm install

# Step 3: Fix module map conflicts
echo "Step 3: Fixing module map conflicts..."
cd ios

# Step 4: Install pods with specific options
echo "Step 4: Installing pods with specific options..."
pod deintegrate
pod setup
pod install --repo-update

# Step 5: Fix module map conflicts after pod install
echo "Step 5: Fixing module map conflicts after pod install..."
find ./Pods/Headers/Public -name "*.modulemap" -exec echo "Found modulemap: {}" \;

# Rename conflicting module maps
if [ -f "./Pods/Headers/Public/ReactCommon/ReactCommon.modulemap" ]; then
  mv "./Pods/Headers/Public/ReactCommon/ReactCommon.modulemap" "./Pods/Headers/Public/ReactCommon/ReactCommon.modulemap.bak"
  echo "Renamed ReactCommon.modulemap to ReactCommon.modulemap.bak"
fi

if [ -f "./Pods/Headers/Public/ReactCommon/React-RuntimeApple.modulemap" ]; then
  mv "./Pods/Headers/Public/ReactCommon/React-RuntimeApple.modulemap" "./Pods/Headers/Public/ReactCommon/React-RuntimeApple.modulemap.bak"
  echo "Renamed React-RuntimeApple.modulemap to React-RuntimeApple.modulemap.bak"
fi

# Step 6: Create a new Podfile with specific configurations
echo "Step 6: Creating a new Podfile with specific configurations..."
cat > Podfile.new << 'EOL'
require_relative '../node_modules/react-native/scripts/react_native_pods'
require_relative '../node_modules/@react-native-community/cli-platform-ios/native_modules'

platform :ios, min_ios_version_supported
prepare_react_native_project!

# Force pods to match minimum iOS version for React Native
def __apply_Xcode_14_3_RC_post_install_workaround(installer)
  installer.pods_project.targets.each do |target|
    target.build_configurations.each do |config|
      current_target = config.build_settings['IPHONEOS_DEPLOYMENT_TARGET']
      minimum_target = min_ios_version_supported
      if current_target.to_f < minimum_target.to_f
        config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = minimum_target
      end
    end
  end
end

target 'PSquare' do
  config = use_native_modules!

  # Flags change depending on the env values.
  flags = get_default_flags()

  use_react_native!(
    :path => config[:reactNativePath],
    # Hermes is now enabled by default. Disable by setting this flag to false.
    :hermes_enabled => flags[:hermes_enabled],
    :fabric_enabled => flags[:fabric_enabled],
    # Enables Flipper.
    #
    # Note that if you have use_frameworks! enabled, Flipper will not work and
    # you should disable the next line.
    :flipper_configuration => FlipperConfiguration.enabled,
    # An absolute path to your application root.
    :app_path => "#{Pod::Config.instance.installation_root}/.."
  )

  post_install do |installer|
    # https://github.com/facebook/react-native/blob/main/packages/react-native/scripts/react_native_pods.rb#L197-L202
    react_native_post_install(
      installer,
      config[:reactNativePath],
      :mac_catalyst_enabled => false
    )
    __apply_Xcode_14_3_RC_post_install_workaround(installer)
    
    # Additional post-install configuration
    installer.pods_project.targets.each do |target|
      target.build_configurations.each do |config|
        config.build_settings['EXPANDED_CODE_SIGN_IDENTITY'] = ""
        config.build_settings['CODE_SIGNING_REQUIRED'] = "NO"
        config.build_settings['CODE_SIGNING_ALLOWED'] = "NO"
        
        # Fix for Xcode 15
        config.build_settings['ENABLE_USER_SCRIPT_SANDBOXING'] = "NO"
        
        # Fix for M1 Macs
        config.build_settings['EXCLUDED_ARCHS[sdk=iphonesimulator*]'] = "arm64"
      end
    end
  end
end
EOL

# Step 7: Replace the Podfile and reinstall pods
echo "Step 7: Replacing the Podfile and reinstalling pods..."
mv Podfile.new Podfile
pod deintegrate
pod install --repo-update

# Step 8: Build the project
echo "Step 8: Building the project..."
xcodebuild -workspace PSquare.xcworkspace -scheme PSquare -configuration Debug -sdk iphonesimulator -quiet

echo "Complete rebuild process finished."
