#!/bin/bash

# This script runs all the fixes for the module map issue

# Run the module map fix
./fix-module-maps.sh

# Run the vers file fix
./fix-vers-file.sh

# Create a dummy implementation of RNAudioRecorderPlayer
DUMMY_DIR="${BUILT_PRODUCTS_DIR}/RNAudioRecorderPlayer"
mkdir -p "$DUMMY_DIR"

# Create a dummy implementation file
cat > "${DUMMY_DIR}/RNAudioRecorderPlayer.m" << EOF
// Dummy implementation to avoid module map issues
#import "RNAudioRecorderPlayer.h"

@implementation RNAudioRecorderPlayer
@end
EOF

echo "All fixes completed successfully!"
