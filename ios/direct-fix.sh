#!/bin/bash

# Find the build directory
BUILD_DIR="$HOME/Library/Developer/Xcode/DerivedData"
PSQUARE_BUILD_DIR=$(find "$BUILD_DIR" -name "PSquare-*" -type d | head -n 1)

if [ -z "$PSQUARE_BUILD_DIR" ]; then
  echo "Could not find PSquare build directory"
  exit 1
fi

echo "Found build directory: $PSQUARE_BUILD_DIR"

# Find all RNAudioRecorderPlayer module map directories
MODULE_MAP_DIRS=$(find "$PSQUARE_BUILD_DIR" -name "RNAudioRecorderPlayer" -type d)

if [ -z "$MODULE_MAP_DIRS" ]; then
  echo "Could not find RNAudioRecorderPlayer directories"
  exit 1
fi

# Create module map file in each directory
for DIR in $MODULE_MAP_DIRS; do
  echo "Creating module map in $DIR"
  
  # Create the module map file
  cat > "$DIR/module.modulemap" << EOF
module RNAudioRecorderPlayer {
  header "RNAudioRecorderPlayer.h"
  export *
}
EOF

  # Copy the header file
  cp "../node_modules/react-native-audio-recorder-player/ios/RNAudioRecorderPlayer.h" "$DIR/"
done

echo "Module maps fixed successfully!"
