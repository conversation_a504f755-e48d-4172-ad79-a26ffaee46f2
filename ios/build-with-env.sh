#!/bin/bash

# Set environment variables to help with sandbox issues
export REACT_NATIVE_SKIP_SANDBOX=1
export NODE_OPTIONS="--max-old-space-size=8192"
export NO_FLIPPER=1

# Clean the project
xcodebuild clean -workspace PSquare.xcworkspace -scheme PSquare

# Build the project
xcodebuild archive -workspace PSquare.xcworkspace -scheme PSquare -configuration Release -archivePath ./build/PSquare.xcarchive
