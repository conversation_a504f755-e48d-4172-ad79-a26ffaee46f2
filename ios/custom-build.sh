#!/bin/bash

# Set environment variables to bypass sandbox restrictions
export REACT_NATIVE_SKIP_SANDBOX=1
export NODE_OPTIONS="--max-old-space-size=8192"
export NO_FLIPPER=1

# Find the path to the React Native scripts
REACT_NATIVE_DIR="$(cd "$(dirname "$0")/../node_modules/react-native" && pwd)"
PROJECT_ROOT="$(cd "$(dirname "$0")/.." && pwd)"

# Create a temporary copy of the React Native build script
TEMP_SCRIPT="$PROJECT_ROOT/ios/temp-react-native-xcode.sh"
cp "$REACT_NATIVE_DIR/scripts/react-native-xcode.sh" "$TEMP_SCRIPT"

# Modify the temporary script to bypass sandbox restrictions
sed -i '' 's/set -e/set -e\nexport REACT_NATIVE_SKIP_SANDBOX=1/' "$TEMP_SCRIPT"

# Make the temporary script executable
chmod +x "$TEMP_SCRIPT"

# Run the modified script
"$TEMP_SCRIPT"

# Clean up
rm "$TEMP_SCRIPT"
