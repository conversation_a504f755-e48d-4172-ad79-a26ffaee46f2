#!/bin/bash

echo "Starting final build process..."

# Step 1: Clean the Pods
echo "Step 1: Cleaning Pods..."
rm -rf Pods
rm -rf build
rm -f Podfile.lock

# Step 2: Install pods
echo "Step 2: Installing pods..."
pod install --repo-update

# Step 3: Fix module map conflicts
echo "Step 3: Fixing module map conflicts..."
find ./Pods/Headers/Public -name "*.modulemap" -exec echo "Found modulemap: {}" \;

# Rename conflicting module maps
if [ -f "./Pods/Headers/Public/ReactCommon/ReactCommon.modulemap" ]; then
  mv "./Pods/Headers/Public/ReactCommon/ReactCommon.modulemap" "./Pods/Headers/Public/ReactCommon/ReactCommon.modulemap.bak"
  echo "Renamed ReactCommon.modulemap to ReactCommon.modulemap.bak"
fi

if [ -f "./Pods/Headers/Public/ReactCommon/React-RuntimeApple.modulemap" ]; then
  mv "./Pods/Headers/Public/ReactCommon/React-RuntimeApple.modulemap" "./Pods/Headers/Public/ReactCommon/React-RuntimeApple.modulemap.bak"
  echo "Renamed React-RuntimeApple.modulemap to React-RuntimeApple.modulemap.bak"
fi

# Step 4: Build the project
echo "Step 4: Building the project..."
xcodebuild -workspace PSquare.xcworkspace -scheme PSquare -configuration Debug -sdk iphonesimulator -quiet

echo "Final build process finished."
