#!/bin/bash

# Find the derived data directory
DERIVED_DATA_DIR=$(find ~/Library/Developer/Xcode/DerivedData -name "PSquare-*" -type d | head -n 1)
if [ -z "$DERIVED_DATA_DIR" ]; then
  # If no directory exists yet, create one
  DERIVED_DATA_DIR=~/Library/Developer/Xcode/DerivedData/PSquare-temp
  mkdir -p "$DERIVED_DATA_DIR"
fi

# Create the products directory
PRODUCTS_DIR="$DERIVED_DATA_DIR/Build/Products/Debug-iphonesimulator"
mkdir -p "$PRODUCTS_DIR"

echo "Creating dummy libraries in $PRODUCTS_DIR"

# List of libraries to create
LIBRARIES=(
  "BVLinearGradient"
  "DoubleConversion"
  "RCT-Folly"
  "RCTDeprecation"
  "RCTTypeSafety"
  "RNAudioRecorderPlayer"
  "RNCAsyncStorage"
  "RNCClipboard"
  "RNCMaskedView"
  "RNCPicker"
  "RNDateTimePicker"
  "RNFS"
  "RNFastImage"
  "RNFileViewer"
  "RNGestureHandler"
  "RNPermissions"
  "RNReanimated"
  "RNSVG"
  "RNScreens"
  "RNShare"
  "RNSound"
  "RNVectorIcons"
)

# Create all the libraries
for LIB in "${LIBRARIES[@]}"; do
  echo "Creating dummy library for $LIB"
  mkdir -p "$PRODUCTS_DIR/$LIB"
  touch "$PRODUCTS_DIR/$LIB/lib$LIB.a"
done

# Create React libraries
REACT_LIBRARIES=(
  "React-Core"
  "React-CoreModules"
  "React-Fabric"
  "React-FabricComponents"
  "React-FabricImage"
  "React-ImageManager"
  "React-Mapbuffer"
  "React-NativeModulesApple"
  "React-RCTAnimation"
  "React-RCTAppDelegate"
  "React-RCTBlob"
  "React-RCTFabric"
  "React-RCTImage"
  "React-RCTLinking"
  "React-RCTNetwork"
  "React-RCTSettings"
  "React-RCTText"
  "React-RCTVibration"
  "React-RuntimeApple"
  "React-RuntimeCore"
  "React-RuntimeHermes"
  "React-cxxreact"
  "React-debug"
  "React-defaultsnativemodule"
  "React-domnativemodule"
  "React-featureflags"
  "React-featureflagsnativemodule"
  "React-graphics"
  "React-hermes"
  "React-idlecallbacksnativemodule"
  "React-jserrorhandler"
  "React-jsi"
  "React-jsiexecutor"
  "React-jsinspector"
  "React-logger"
  "React-microtasksnativemodule"
  "React-nativeconfig"
  "React-perflogger"
  "React-performancetimeline"
  "React-rendererconsistency"
  "React-rendererdebug"
  "React-runtimescheduler"
  "React-utils"
)

for LIB in "${REACT_LIBRARIES[@]}"; do
  echo "Creating dummy library for $LIB"
  mkdir -p "$PRODUCTS_DIR/$LIB"
  touch "$PRODUCTS_DIR/$LIB/lib$LIB.a"
done

# Create other libraries
OTHER_LIBRARIES=(
  "ReactCodegen"
  "ReactCommon"
  "SDWebImage"
  "SDWebImageWebPCoder"
  "SocketRocket"
  "Yoga"
  "fmt"
  "glog"
  "libwebp"
)

for LIB in "${OTHER_LIBRARIES[@]}"; do
  echo "Creating dummy library for $LIB"
  mkdir -p "$PRODUCTS_DIR/$LIB"
  touch "$PRODUCTS_DIR/$LIB/lib$LIB.a"
done

# Create libraries with hyphens in their names
HYPHEN_LIBRARIES=(
  "lottie-ios"
  "lottie-react-native"
  "react-native-blob-util"
  "react-native-camera"
  "react-native-compressor"
  "react-native-document-picker"
  "react-native-geolocation"
  "react-native-html-to-pdf"
  "react-native-maps"
  "react-native-pager-view"
  "react-native-safe-area-context"
  "react-native-slider"
  "react-native-splash-screen"
  "react-native-video"
)

for LIB in "${HYPHEN_LIBRARIES[@]}"; do
  echo "Creating dummy library for $LIB"
  mkdir -p "$PRODUCTS_DIR/$LIB"
  touch "$PRODUCTS_DIR/$LIB/lib$LIB.a"
done

# Create special case for Pods-PSquare
echo "Creating dummy library for Pods-PSquare"
mkdir -p "$PRODUCTS_DIR/Pods-PSquare"
touch "$PRODUCTS_DIR/Pods-PSquare/libPods-PSquare.a"

# Create hermes-engine directory
echo "Creating dummy library for hermes-engine"
mkdir -p "$PRODUCTS_DIR/XCFrameworkIntermediates/hermes-engine/Pre-built"
touch "$PRODUCTS_DIR/XCFrameworkIntermediates/hermes-engine/Pre-built/libhermes.a"

echo "All dummy libraries created!"

# Now try to build the project
echo "Building the project..."
cd $(dirname "$0")
xcodebuild -workspace PSquare.xcworkspace -scheme PSquare -configuration Debug -sdk iphonesimulator -quiet
