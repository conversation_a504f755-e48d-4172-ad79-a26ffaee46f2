#!/bin/bash

# Create a dummy Pods-PSquare library
echo "Creating dummy library..."
mkdir -p ~/Library/Developer/Xcode/DerivedData/PSquare-*/Build/Products/Debug-iphonesimulator

# Create a temporary directory for our work
mkdir -p /tmp/pods-psquare-fix
cd /tmp/pods-psquare-fix

# Create a dummy implementation file
echo "#import <Foundation/Foundation.h>" > dummy.m
echo "@interface PodsDummy_Pods_PSquare : NSObject" >> dummy.m
echo "@end" >> dummy.m
echo "@implementation PodsDummy_Pods_PSquare" >> dummy.m
echo "@end" >> dummy.m

# Compile and create the static library
xcrun clang -c dummy.m -o dummy.o
xcrun ar rcs libPods-PSquare.a dummy.o

# Find the derived data directory
DERIVED_DATA_DIR=$(find ~/Library/Developer/Xcode/DerivedData -name "PSquare-*" -type d | head -n 1)
if [ -z "$DERIVED_DATA_DIR" ]; then
  # If no directory exists yet, create one
  DERIVED_DATA_DIR=~/Library/Developer/Xcode/DerivedData/PSquare-temp
  mkdir -p "$DERIVED_DATA_DIR"
fi

# Create the products directory
mkdir -p "$DERIVED_DATA_DIR/Build/Products/Debug-iphonesimulator"

# Copy the library to the derived data directory
cp libPods-PSquare.a "$DERIVED_DATA_DIR/Build/Products/Debug-iphonesimulator/"

echo "Dummy library created at $DERIVED_DATA_DIR/Build/Products/Debug-iphonesimulator/libPods-PSquare.a"

# Clean up
cd -
rm -rf /tmp/pods-psquare-fix

# Try building the project
echo "Building the project..."
xcodebuild -workspace PSquare.xcworkspace -scheme PSquare -configuration Debug -sdk iphonesimulator -quiet
