#!/bin/bash

echo "Creating ReactCommon.modulemap file..."

# Create the directory if it doesn't exist
mkdir -p ./Pods/Headers/Public/ReactCommon

# Create the modulemap file
cat > ./Pods/Headers/Public/ReactCommon/ReactCommon.modulemap << 'EOL'
module ReactCommon {
  umbrella header "ReactCommon.h"
  export *
  module * { export * }
}
EOL

# Create a dummy header file
cat > ./Pods/Headers/Public/ReactCommon/ReactCommon.h << 'EOL'
// Dummy header file for ReactCommon
#ifndef ReactCommon_h
#define ReactCommon_h

#endif /* ReactCommon_h */
EOL

echo "ReactCommon.modulemap created successfully."
