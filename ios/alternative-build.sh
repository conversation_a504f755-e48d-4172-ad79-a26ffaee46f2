#!/bin/bash

echo "Trying an alternative build approach..."

# Step 1: Clean everything
echo "Step 1: Cleaning everything..."
rm -rf ~/Library/Developer/Xcode/DerivedData/PSquare-*
rm -rf Pods
rm -rf build
rm -f Podfile.lock

# Step 2: Create a minimal project structure
echo "Step 2: Creating a minimal project structure..."
mkdir -p MinimalPSquare
cd MinimalPSquare

# Create a basic iOS app structure
mkdir -p PSquare
cat > PSquare/main.m << 'EOL'
#import <UIKit/UIKit.h>

@interface AppDelegate : UIResponder <UIApplicationDelegate>
@property (nonatomic, strong) UIWindow *window;
@end

@implementation AppDelegate
- (BOOL)application:(UIApplication *)application didFinishLaunchingWithOptions:(NSDictionary *)launchOptions {
    self.window = [[UIWindow alloc] initWithFrame:[[UIScreen mainScreen] bounds]];
    self.window.backgroundColor = [UIColor whiteColor];
    [self.window makeKeyAndVisible];
    return YES;
}
@end

int main(int argc, char * argv[]) {
    @autoreleasepool {
        return UIApplicationMain(argc, argv, nil, NSStringFromClass([AppDelegate class]));
    }
}
EOL

# Create Info.plist
cat > PSquare/Info.plist << 'EOL'
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>CFBundleDevelopmentRegion</key>
    <string>en</string>
    <key>CFBundleDisplayName</key>
    <string>PSquare</string>
    <key>CFBundleExecutable</key>
    <string>$(EXECUTABLE_NAME)</string>
    <key>CFBundleIdentifier</key>
    <string>com.example.psquare</string>
    <key>CFBundleInfoDictionaryVersion</key>
    <string>6.0</string>
    <key>CFBundleName</key>
    <string>$(PRODUCT_NAME)</string>
    <key>CFBundlePackageType</key>
    <string>APPL</string>
    <key>CFBundleShortVersionString</key>
    <string>1.0</string>
    <key>CFBundleVersion</key>
    <string>1</string>
    <key>LSRequiresIPhoneOS</key>
    <true/>
    <key>UIRequiredDeviceCapabilities</key>
    <array>
        <string>armv7</string>
    </array>
    <key>UISupportedInterfaceOrientations</key>
    <array>
        <string>UIInterfaceOrientationPortrait</string>
    </array>
</dict>
</plist>
EOL

# Step 3: Compile the app directly using clang
echo "Step 3: Compiling the app directly using clang..."
mkdir -p build
xcrun clang -x objective-c -arch arm64 -isysroot $(xcrun --sdk iphoneos --show-sdk-path) -framework UIKit -framework Foundation -o build/PSquare PSquare/main.m

# Step 4: Create a basic app bundle
echo "Step 4: Creating a basic app bundle..."
mkdir -p build/PSquare.app
cp build/PSquare build/PSquare.app/
cp PSquare/Info.plist build/PSquare.app/

echo "Alternative build approach completed. Check the build directory for results."