#!/usr/bin/env ruby

require 'xcodeproj'

puts "Disabling Pods-PSquare dependency in Xcode project..."

# Open the project
project_path = 'PSquare.xcodeproj'
project = Xcodeproj::Project.open(project_path)

# Find the main target
main_target = project.targets.find { |t| t.name == 'PSquare' }

if main_target.nil?
  puts "Error: Could not find PSquare target"
  exit 1
end

puts "Found PSquare target, removing Pods-PSquare dependency..."

# Remove the Pods-PSquare dependency
main_target.dependencies.each do |dependency|
  if dependency.target && dependency.target.name == 'Pods-PSquare'
    main_target.dependencies.delete(dependency)
    puts "Removed Pods-PSquare dependency"
  end
end

# Find the link binary with libraries build phase
link_phase = main_target.build_phases.find { |phase| phase.is_a?(Xcodeproj::Project::Object::PBXFrameworksBuildPhase) }

if link_phase
  # Remove any references to libPods-PSquare.a
  link_phase.files.each do |build_file|
    if build_file.file_ref && build_file.file_ref.path && build_file.file_ref.path.include?('libPods-PSquare.a')
      link_phase.files.delete(build_file)
      puts "Removed libPods-PSquare.a from link phase"
    end
  end
end

# Modify build settings for all configurations
main_target.build_configurations.each do |config|
  # Remove any references to Pods-PSquare from the linker flags
  if config.build_settings['OTHER_LDFLAGS']
    config.build_settings['OTHER_LDFLAGS'] = config.build_settings['OTHER_LDFLAGS'].reject { |flag| flag.include?('Pods-PSquare') }
    puts "Removed Pods-PSquare from OTHER_LDFLAGS in #{config.name} configuration"
  end
  
  # Add direct references to the essential libraries
  essential_libs = ['-lReact-Core', '-lReact-RCTAnimation', '-lReact-RCTBlob', '-lReact-RCTImage', 
                   '-lReact-RCTLinking', '-lReact-RCTNetwork', '-lReact-RCTSettings', '-lReact-RCTText', 
                   '-lReact-RCTVibration', '-lReact-cxxreact', '-lReact-jsi', '-lReact-jsiexecutor', 
                   '-lReact-jsinspector', '-lReact-logger', '-lReact-perflogger', '-lYoga']
  
  if config.build_settings['OTHER_LDFLAGS'].nil?
    config.build_settings['OTHER_LDFLAGS'] = ['$(inherited)', '-ObjC'] + essential_libs
  else
    config.build_settings['OTHER_LDFLAGS'] += essential_libs
  end
  
  puts "Added essential libraries to OTHER_LDFLAGS in #{config.name} configuration"
end

# Save the project
project.save

puts "Xcode project updated successfully."

# Now let's also modify the Pods project to disable the Pods-PSquare target
pods_project_path = 'Pods/Pods.xcodeproj'
if File.exist?(pods_project_path)
  puts "Modifying Pods project..."
  
  pods_project = Xcodeproj::Project.open(pods_project_path)
  
  # Find the Pods-PSquare target
  pods_target = pods_project.targets.find { |t| t.name == 'Pods-PSquare' }
  
  if pods_target
    puts "Found Pods-PSquare target, modifying build settings..."
    
    # Modify build settings for all configurations to make it a dummy target
    pods_target.build_configurations.each do |config|
      # Set MACH_O_TYPE to staticlib to ensure it builds a static library
      config.build_settings['MACH_O_TYPE'] = 'staticlib'
      
      # Set ONLY_ACTIVE_ARCH to YES to simplify the build
      config.build_settings['ONLY_ACTIVE_ARCH'] = 'YES'
      
      # Set SKIP_INSTALL to NO to ensure it's installed
      config.build_settings['SKIP_INSTALL'] = 'NO'
      
      # Simplify the build settings
      config.build_settings['LIBRARY_SEARCH_PATHS'] = ['$(inherited)']
      config.build_settings['HEADER_SEARCH_PATHS'] = ['$(inherited)']
      config.build_settings['OTHER_LDFLAGS'] = ['$(inherited)', '-ObjC']
      
      puts "Updated build settings for #{config.name} configuration in Pods-PSquare target"
    end
    
    # Create a dummy source file for Pods-PSquare
    dummy_file_path = 'Pods/Target Support Files/Pods-PSquare/Pods-PSquare-dummy.m'
    unless File.exist?(dummy_file_path)
      File.open(dummy_file_path, 'w') do |file|
        file.puts "// Dummy source file for Pods-PSquare"
        file.puts "#import <Foundation/Foundation.h>"
        file.puts ""
        file.puts "// Dummy symbol to ensure the static library is not empty"
        file.puts "void PodsPSquareDummySymbol(void) {}"
      end
      puts "Created dummy source file for Pods-PSquare"
    end
  else
    puts "Warning: Could not find Pods-PSquare target in Pods project"
  end
  
  # Save the Pods project
  pods_project.save
  
  puts "Pods project updated successfully."
end

puts "All modifications complete. Try building the project now."
