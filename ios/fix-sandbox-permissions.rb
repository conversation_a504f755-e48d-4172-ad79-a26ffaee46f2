#!/usr/bin/env ruby

require 'xcodeproj'

puts "Fixing sandbox permissions in Xcode project..."

# Open the project
project_path = 'PSquare.xcodeproj'
project = Xcodeproj::Project.open(project_path)

# Find the main target
main_target = project.targets.find { |t| t.name == 'PSquare' }

if main_target.nil?
  puts "Error: Could not find PSquare target"
  exit 1
end

puts "Found PSquare target, modifying build phases..."

# Find all script phases
script_phases = main_target.build_phases.select { |phase| phase.is_a?(Xcodeproj::Project::Object::PBXShellScriptBuildPhase) }

script_phases.each do |phase|
  if phase.shell_script && (phase.shell_script.include?('node') || phase.shell_script.include?('react-native'))
    puts "Found script phase: #{phase.name || 'Unnamed'}"
    
    # Add sandbox permissions
    modified_script = "export PATH=\"$PATH:/usr/local/bin:/opt/homebrew/bin\"\n"
    modified_script += "export NODE_BINARY=node\n"
    modified_script += "export EXTRA_PACKAGER_ARGS=\"--max-workers=4\"\n"
    modified_script += "export SKIP_BUNDLING=true\n"  # Skip bundling to avoid sandbox issues
    modified_script += phase.shell_script
    
    phase.shell_script = modified_script
    puts "Modified script phase to skip bundling and set environment variables"
  end
end

# Save the project
project.save

puts "Xcode project updated successfully."

# Now let's create a dummy BVLinearGradient directory
puts "Creating dummy BVLinearGradient directory..."
system("mkdir -p ios/BVLinearGradient")
system("touch ios/BVLinearGradient/.gitkeep")

puts "All modifications complete. Try building the project now."
