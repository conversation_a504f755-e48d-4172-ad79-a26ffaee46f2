#!/bin/bash

echo "Fixing module map conflicts..."

# Rename the conflicting module map
if [ -f "/Users/<USER>/CodeBase/Apoint/Mobile-App/psquare_mobile_app/ios/Pods/Headers/Public/ReactCommon/ReactCommon.modulemap" ]; then
  mv "/Users/<USER>/CodeBase/Apoint/Mobile-App/psquare_mobile_app/ios/Pods/Headers/Public/ReactCommon/ReactCommon.modulemap" "/Users/<USER>/CodeBase/Apoint/Mobile-App/psquare_mobile_app/ios/Pods/Headers/Public/ReactCommon/ReactCommon.modulemap.bak"
  echo "Renamed ReactCommon.modulemap to ReactCommon.modulemap.bak"
fi

echo "Opening the project in Xcode..."
open PSquare.xcworkspace
