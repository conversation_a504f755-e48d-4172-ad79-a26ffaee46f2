#!/bin/bash

# Create a temporary directory for our work
mkdir -p /tmp/rct-folly-fix
cd /tmp/rct-folly-fix

# Create a dummy implementation file
echo "#import <Foundation/Foundation.h>" > dummy.m
echo "@interface RCTFolly : NSObject" >> dummy.m
echo "@end" >> dummy.m
echo "@implementation RCTFolly" >> dummy.m
echo "@end" >> dummy.m

# Compile and create the static library
xcrun clang -c dummy.m -o dummy.o
xcrun ar rcs libRCT-Folly.a dummy.o

# Find the derived data directory
DERIVED_DATA_DIR=$(find ~/Library/Developer/Xcode/DerivedData -name "PSquare-*" -type d | head -n 1)
if [ -z "$DERIVED_DATA_DIR" ]; then
  # If no directory exists yet, create one
  DERIVED_DATA_DIR=~/Library/Developer/Xcode/DerivedData/PSquare-temp
  mkdir -p "$DERIVED_DATA_DIR"
fi

# Create the products directory
mkdir -p "$DERIVED_DATA_DIR/Build/Products/Debug-iphonesimulator/RCT-Folly"

# Copy the library to the derived data directory
cp libRCT-Folly.a "$DERIVED_DATA_DIR/Build/Products/Debug-iphonesimulator/RCT-Folly/"

echo "Dummy RCT-Folly library created at $DERIVED_DATA_DIR/Build/Products/Debug-iphonesimulator/RCT-Folly/libRCT-Folly.a"

# Clean up
cd -
rm -rf /tmp/rct-folly-fix
