#!/bin/bash

# Create a backup of the original podspec
cp "../node_modules/react-native-audio-recorder-player/RNAudioRecorderPlayer.podspec" "../node_modules/react-native-audio-recorder-player/RNAudioRecorderPlayer.podspec.bak"

# Copy the patched podspec
cp "RNAudioRecorderPlayer.podspec.patch" "../node_modules/react-native-audio-recorder-player/RNAudioRecorderPlayer.podspec"

# Create the module map file
cat > "../node_modules/react-native-audio-recorder-player/RNAudioRecorderPlayer.modulemap" << EOF
module RNAudioRecorderPlayer {
  header "ios/RNAudioRecorderPlayer.h"
  export *
}
EOF

echo "Podspec patched successfully!"
