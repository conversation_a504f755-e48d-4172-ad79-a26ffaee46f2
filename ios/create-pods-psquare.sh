#!/bin/bash

# Find the derived data directory
DERIVED_DATA_DIR=$(find ~/Library/Developer/Xcode/DerivedData -name "PSquare-*" -type d | head -n 1)
if [ -z "$DERIVED_DATA_DIR" ]; then
  echo "No DerivedData directory found for PSquare. Creating a temporary one."
  DERIVED_DATA_DIR=~/Library/Developer/Xcode/DerivedData/PSquare-temp
  mkdir -p "$DERIVED_DATA_DIR"
fi

# Create the products directory
PRODUCTS_DIR="$DERIVED_DATA_DIR/Build/Products/Debug-iphonesimulator"
mkdir -p "$PRODUCTS_DIR/Pods-PSquare"

echo "Creating Pods-PSquare library in $PRODUCTS_DIR/Pods-PSquare"

# Create a temporary directory for our work
mkdir -p /tmp/pods-psquare-fix
cd /tmp/pods-psquare-fix

# Create a dummy implementation file
cat > dummy.m << 'EOL'
#import <Foundation/Foundation.h>

@interface PodsPSquare : NSObject
@end

@implementation PodsPSquare
@end
EOL

# Compile and create the static library
xcrun clang -c dummy.m -o dummy.o
xcrun ar rcs libPods-PSquare.a dummy.o

# Copy the library to the derived data directory
cp libPods-PSquare.a "$PRODUCTS_DIR/Pods-PSquare/"

echo "Pods-PSquare library created at $PRODUCTS_DIR/Pods-PSquare/libPods-PSquare.a"

# Clean up
cd -
rm -rf /tmp/pods-psquare-fix
