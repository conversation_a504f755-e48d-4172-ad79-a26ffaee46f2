#!/bin/bash

# Find the path to the React Native scripts
REACT_NATIVE_DIR="$(cd "$(dirname "$0")/../node_modules/react-native" && pwd)"
SCRIPT_PATH="$REACT_NATIVE_DIR/scripts/react-native-xcode.sh"
PATCH_PATH="$(cd "$(dirname "$0")" && pwd)/react-native-xcode.patch"

# Create a backup of the original script
cp "$SCRIPT_PATH" "$SCRIPT_PATH.bak"

# Apply the patch
patch "$SCRIPT_PATH" < "$PATCH_PATH"

# Make the script executable
chmod +x "$SCRIPT_PATH"

echo "Patch applied successfully!"
