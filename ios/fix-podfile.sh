#!/bin/bash

echo "Creating a simplified Podfile to fix dependency issues..."

# Create a backup of the original Podfile
cp Podfile Podfile.original
echo "Created backup at Podfile.original"

# Create a simplified Podfile
cat > Podfile << 'EOL'
require_relative '../node_modules/react-native/scripts/react_native_pods'
require_relative '../node_modules/@react-native-community/cli-platform-ios/native_modules'

platform :ios, '15.1'
prepare_react_native_project!

# Disable Flipper which is a common source of issues
flipper_config = nil

linkage = ENV['USE_FRAMEWORKS']
if linkage != nil
  Pod::UI.puts "Configuring Pod with #{linkage}ally linked Frameworks".green
  use_frameworks! :linkage => linkage.to_sym
end

target 'PSquare' do
  config = use_native_modules!
  use_react_native!(
    :path => config[:reactNativePath],
    # Disable Hermes for now to simplify the build
    :hermes_enabled => false,
    :fabric_enabled => false
    # Flipper is disabled by setting flipper_config to nil
  )

  # Manually specify the essential dependencies
  pod 'React-Core', :path => '../node_modules/react-native/'
  pod 'React-RCTAppDelegate', :path => '../node_modules/react-native/Libraries/AppDelegate'

  # Add any other essential pods here

  # Disable post_install hooks that might cause issues
  post_install do |installer|
    # No complex post-install steps
    installer.pods_project.targets.each do |target|
      target.build_configurations.each do |config|
        config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '15.1'
      end
    end
  end
end
EOL

echo "Simplified Podfile created. Now running pod install..."

# Run pod install with the new Podfile
pod install

echo "Pod installation completed. Now let's create a dummy Pods-PSquare target..."

# Create a dummy Pods-PSquare.xcconfig file
mkdir -p Pods/Target\ Support\ Files/Pods-PSquare
cat > "Pods/Target Support Files/Pods-PSquare/Pods-PSquare.debug.xcconfig" << 'EOL'
FRAMEWORK_SEARCH_PATHS = $(inherited)
GCC_PREPROCESSOR_DEFINITIONS = $(inherited) COCOAPODS=1
HEADER_SEARCH_PATHS = $(inherited) "${PODS_ROOT}/Headers/Public" "${PODS_ROOT}/Headers/Public/React-Core"
LIBRARY_SEARCH_PATHS = $(inherited) "${PODS_CONFIGURATION_BUILD_DIR}/React-Core" "${SRCROOT}/build/Debug-iphonesimulator" "${SRCROOT}/build/Debug-iphonesimulator/Pods-PSquare"
OTHER_LDFLAGS = $(inherited) -ObjC -l"Pods-PSquare" -l"React-Core" -framework "JavaScriptCore" -framework "UIKit"
PODS_BUILD_DIR = ${BUILD_DIR}
PODS_CONFIGURATION_BUILD_DIR = ${PODS_BUILD_DIR}/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)
PODS_ROOT = ${SRCROOT}/Pods
USE_RECURSIVE_SCRIPT_INPUTS_IN_SCRIPT_PHASES = YES
EOL

# Copy the debug config to release config
cp "Pods/Target Support Files/Pods-PSquare/Pods-PSquare.debug.xcconfig" "Pods/Target Support Files/Pods-PSquare/Pods-PSquare.release.xcconfig"

echo "Dummy Pods-PSquare configuration created."
