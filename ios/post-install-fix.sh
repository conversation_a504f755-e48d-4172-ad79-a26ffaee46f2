#!/bin/bash

# Find the Pods directory
PODS_ROOT="$SRCROOT/Pods"
if [ ! -d "$PODS_ROOT" ]; then
  PODS_ROOT="./Pods"
fi

# Create the module map directory
mkdir -p "$PODS_ROOT/Headers/Public/RNAudioRecorderPlayer"

# Create the module map file
cat > "$PODS_ROOT/Headers/Public/RNAudioRecorderPlayer/module.modulemap" << EOF
module RNAudioRecorderPlayer {
  header "RNAudioRecorderPlayer.h"
  export *
}
EOF

# Create a symbolic link to the header file
NODE_MODULES_PATH="../node_modules"
if [ ! -d "$NODE_MODULES_PATH" ]; then
  NODE_MODULES_PATH="../../node_modules"
fi

ln -sf "$NODE_MODULES_PATH/react-native-audio-recorder-player/ios/RNAudioRecorderPlayer.h" "$PODS_ROOT/Headers/Public/RNAudioRecorderPlayer/RNAudioRecorderPlayer.h"

echo "Module map fixed successfully!"
