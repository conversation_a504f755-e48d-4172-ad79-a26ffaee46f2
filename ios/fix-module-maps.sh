#!/bin/bash

# This script fixes the module map issue for RNAudioRecorderPlayer

# Create directories for module maps in multiple possible locations
mkdir -p "${PODS_ROOT}/Headers/Public/RNAudioRecorderPlayer"
mkdir -p "${BUILT_PRODUCTS_DIR}/RNAudioRecorderPlayer"

# Create module map for RNAudioRecorderPlayer in Pods directory
cat > "${PODS_ROOT}/Headers/Public/RNAudioRecorderPlayer/module.modulemap" << EOF
module RNAudioRecorderPlayer {
  header "RNAudioRecorderPlayer.h"
  export *
}
EOF

# Create module map for RNAudioRecorderPlayer in build directory
cat > "${BUILT_PRODUCTS_DIR}/RNAudioRecorderPlayer/module.modulemap" << EOF
module RNAudioRecorderPlayer {
  header "RNAudioRecorderPlayer.h"
  export *
}
EOF

# Copy the header file to both locations
cp "${SRCROOT}/../node_modules/react-native-audio-recorder-player/ios/RNAudioRecorderPlayer.h" "${PODS_ROOT}/Headers/Public/RNAudioRecorderPlayer/"
cp "${SRCROOT}/../node_modules/react-native-audio-recorder-player/ios/RNAudioRecorderPlayer.h" "${BUILT_PRODUCTS_DIR}/RNAudioRecorderPlayer/"

# Also create a symbolic link as a backup
ln -sf "${SRCROOT}/../node_modules/react-native-audio-recorder-player/ios/RNAudioRecorderPlayer.h" "${PODS_ROOT}/Headers/Public/RNAudioRecorderPlayer/RNAudioRecorderPlayer.h"

# Fix for Xcode DerivedData directory
DERIVED_DATA_DIR="${HOME}/Library/Developer/Xcode/DerivedData"
PSQUARE_BUILD_DIR=$(find "${DERIVED_DATA_DIR}" -name "PSquare-*" -type d 2>/dev/null | head -n 1)

if [ -n "${PSQUARE_BUILD_DIR}" ]; then
  # Create module map in all possible build directories
  find "${PSQUARE_BUILD_DIR}" -name "Debug-*" -type d | while read -r BUILD_DIR; do
    mkdir -p "${BUILD_DIR}/RNAudioRecorderPlayer"

    # Create the module map file
    cat > "${BUILD_DIR}/RNAudioRecorderPlayer/module.modulemap" << EOF
module RNAudioRecorderPlayer {
  header "RNAudioRecorderPlayer.h"
  export *
}
EOF

    # Copy the header file
    cp "${SRCROOT}/../node_modules/react-native-audio-recorder-player/ios/RNAudioRecorderPlayer.h" "${BUILD_DIR}/RNAudioRecorderPlayer/"
  done
fi

echo "Module maps fixed successfully!"
