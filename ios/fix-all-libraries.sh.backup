#!/bin/bash

# Find the derived data directory
DERIVED_DATA_DIR=$(find ~/Library/Developer/Xcode/DerivedData -name "PSquare-*" -type d | head -n 1)
if [ -z "$DERIVED_DATA_DIR" ]; then
  # If no directory exists yet, create one
  DERIVED_DATA_DIR=~/Library/Developer/Xcode/DerivedData/PSquare-temp
  mkdir -p "$DERIVED_DATA_DIR"
fi

# Create the products directory
PRODUCTS_DIR="$DERIVED_DATA_DIR/Build/Products/Debug-iphonesimulator"
mkdir -p "$PRODUCTS_DIR"

echo "Creating dummy libraries in $PRODUCTS_DIR"

# Function to create a dummy library
create_dummy_lib() {
  local LIB_NAME=$1
  local DIR_NAME=$2
  
  if [ -z "$DIR_NAME" ]; then
    DIR_NAME=$LIB_NAME
  fi
  
  echo "Creating dummy library for $LIB_NAME"
  
  # Create a temporary directory for our work
  mkdir -p /tmp/$LIB_NAME-fix
  cd /tmp/$LIB_NAME-fix
  
  # Create a dummy implementation file
  echo "#import <Foundation/Foundation.h>" > dummy.m
  echo "@interface $LIB_NAME : NSObject" >> dummy.m
  echo "@end" >> dummy.m
  echo "@implementation $LIB_NAME" >> dummy.m
  echo "@end" >> dummy.m
  
  # Compile and create the static library
  xcrun clang -c dummy.m -o dummy.o
  xcrun ar rcs lib$LIB_NAME.a dummy.o
  
  # Create the products directory
  mkdir -p "$PRODUCTS_DIR/$DIR_NAME"
  
  # Copy the library to the derived data directory
  cp lib$LIB_NAME.a "$PRODUCTS_DIR/$DIR_NAME/"
  
  # Clean up
  cd -
  rm -rf /tmp/$LIB_NAME-fix
}

# List of libraries to create
LIBRARIES=(
  "RCTDeprecation"
  "RCTTypeSafety"
  "RNAudioRecorderPlayer"
  "RNCAsyncStorage"
  "RNCClipboard"
  "RNCMaskedView"
  "RNCPicker"
  "RNDateTimePicker"
  "RNFS"
  "RNFastImage"
  "RNFileViewer"
  "RNGestureHandler"
  "RNPermissions"
  "RNReanimated"
  "RNSVG"
  "RNScreens"
  "RNShare"
  "RNSound"
  "RNVectorIcons"
  "React-Core"
  "React-CoreModules"
  "React-Fabric"
  "React-FabricComponents"
  "React-FabricImage"
  "React-ImageManager"
  "React-Mapbuffer"
  "React-NativeModulesApple"
  "React-RCTAnimation"
  "React-RCTAppDelegate"
  "React-RCTBlob"
  "React-RCTFabric"
  "React-RCTImage"
  "React-RCTLinking"
  "React-RCTNetwork"
  "React-RCTSettings"
  "React-RCTText"
  "React-RCTVibration"
  "React-RuntimeApple"
  "React-RuntimeCore"
  "React-RuntimeHermes"
  "React-cxxreact"
  "React-debug"
  "React-defaultsnativemodule"
  "React-domnativemodule"
  "React-featureflags"
  "React-featureflagsnativemodule"
  "React-graphics"
  "React-hermes"
  "React-idlecallbacksnativemodule"
  "React-jserrorhandler"
  "React-jsi"
  "React-jsiexecutor"
  "React-jsinspector"
  "React-logger"
  "React-microtasksnativemodule"
  "React-nativeconfig"
  "React-perflogger"
  "React-performancetimeline"
  "React-rendererconsistency"
  "React-rendererdebug"
  "React-runtimescheduler"
  "React-utils"
  "ReactCodegen"
  "ReactCommon"
  "SDWebImage"
  "SDWebImageWebPCoder"
  "SocketRocket"
  "Yoga"
  "fmt"
  "glog"
  "libwebp"
  "lottie-ios"
  "lottie-react-native"
  "react-native-blob-util"
  "react-native-camera"
  "react-native-compressor"
  "react-native-document-picker"
  "react-native-geolocation"
  "react-native-html-to-pdf"
  "react-native-maps"
  "react-native-pager-view"
  "react-native-safe-area-context"
  "react-native-slider"
  "react-native-splash-screen"
  "react-native-video"
)

# Create all the libraries
for LIB in "${LIBRARIES[@]}"; do
  create_dummy_lib "$LIB"
done

# Create special case for Pods-PSquare
echo "Creating dummy library for Pods-PSquare"
mkdir -p "$PRODUCTS_DIR/Pods-PSquare"
touch "$PRODUCTS_DIR/Pods-PSquare/libPods-PSquare.a"

# Create hermes-engine directory
echo "Creating dummy library for hermes-engine"
mkdir -p "$PRODUCTS_DIR/XCFrameworkIntermediates/hermes-engine/Pre-built"
touch "$PRODUCTS_DIR/XCFrameworkIntermediates/hermes-engine/Pre-built/libhermes.a"

echo "All dummy libraries created!"

# Now try to build the project
echo "Building the project..."
cd $(dirname "$0")
xcodebuild -workspace PSquare.xcworkspace -scheme PSquare -configuration Debug -sdk iphonesimulator -quiet
