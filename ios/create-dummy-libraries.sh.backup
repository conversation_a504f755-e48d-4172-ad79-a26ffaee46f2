#!/bin/bash

# Create dummy libraries for all the missing libraries
DERIVED_DATA_DIR=~/Library/Developer/Xcode/DerivedData/PSquare-aoohziwnmgtharelebuojgenmmkx/Build/Products/Debug-iphonesimulator

# List of libraries to create
LIBRARIES=(
  "RCTDeprecation"
  "RCTTypeSafety"
  "RNAudioRecorderPlayer"
  "RNCAsyncStorage"
  "RNCClipboard"
  "RNCMaskedView"
  "RNCPicker"
  "RNDateTimePicker"
  "RNFS"
  "RNFastImage"
  "RNFileViewer"
  "RNGestureHandler"
  "RNPermissions"
  "RNReanimated"
  "RNSVG"
  "RNScreens"
  "RNShare"
  "RNSound"
  "RNVectorIcons"
  "React-Core"
  "React-CoreModules"
  "React-Fabric"
  "React-FabricComponents"
  "React-FabricImage"
  "React-ImageManager"
  "React-Mapbuffer"
  "React-NativeModulesApple"
  "React-RCTAnimation"
  "React-RCTAppDelegate"
  "React-RCTBlob"
  "React-RCTFabric"
  "React-RCTImage"
  "React-RCTLinking"
  "React-RCTNetwork"
  "React-RCTSettings"
  "React-RCTText"
  "React-RCTVibration"
  "React-RuntimeApple"
  "React-RuntimeCore"
  "React-RuntimeHermes"
  "React-cxxreact"
  "React-debug"
  "React-defaultsnativemodule"
  "React-domnativemodule"
  "React-featureflags"
  "React-featureflagsnativemodule"
  "React-graphics"
  "React-hermes"
  "React-idlecallbacksnativemodule"
  "React-jserrorhandler"
  "React-jsi"
  "React-jsiexecutor"
  "React-jsinspector"
  "React-logger"
  "React-microtasksnativemodule"
  "React-nativeconfig"
  "React-perflogger"
  "React-performancetimeline"
  "React-rendererconsistency"
  "React-rendererdebug"
  "React-runtimescheduler"
  "React-utils"
  "ReactCodegen"
  "ReactCommon"
  "SDWebImage"
  "SDWebImageWebPCoder"
  "SocketRocket"
  "Yoga"
  "fmt"
  "glog"
  "libwebp"
  "lottie-ios"
  "lottie-react-native"
  "react-native-blob-util"
  "react-native-camera"
  "react-native-compressor"
  "react-native-document-picker"
  "react-native-geolocation"
  "react-native-html-to-pdf"
  "react-native-maps"
  "react-native-pager-view"
  "react-native-safe-area-context"
  "react-native-slider"
  "react-native-splash-screen"
  "react-native-video"
)

# Create directories and dummy libraries
for LIB in "${LIBRARIES[@]}"; do
  mkdir -p "$DERIVED_DATA_DIR/$LIB"
  touch "$DERIVED_DATA_DIR/$LIB/lib$LIB.a"
  echo "Created dummy library for $LIB"
done

# Create hermes-engine directory
mkdir -p "$DERIVED_DATA_DIR/XCFrameworkIntermediates/hermes-engine/Pre-built"
touch "$DERIVED_DATA_DIR/XCFrameworkIntermediates/hermes-engine/Pre-built/libhermes.a"
echo "Created dummy library for hermes-engine"

echo "All dummy libraries created!"
