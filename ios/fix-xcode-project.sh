#!/bin/bash

echo "Fixing Xcode project to use our custom Pods-PSquare library..."

# Find the Xcode project file
PROJECT_FILE="PSquare.xcodeproj/project.pbxproj"
if [ ! -f "$PROJECT_FILE" ]; then
  echo "Error: Project file not found. Make sure you're in the iOS project directory."
  exit 1
fi

# Create a backup of the project file
cp "$PROJECT_FILE" "${PROJECT_FILE}.backup"
echo "Created backup at ${PROJECT_FILE}.backup"

# Create a directory for our custom library
mkdir -p "build/Debug-iphonesimulator/Pods-PSquare"

# Copy our library to the build directory
cp ~/Library/Developer/Xcode/DerivedData/PSquare-*/Build/Products/Debug-iphonesimulator/libPods-PSquare.a build/Debug-iphonesimulator/
cp ~/Library/Developer/Xcode/DerivedData/PSquare-*/Build/Products/Debug-iphonesimulator/libPods-PSquare.a build/Debug-iphonesimulator/Pods-PSquare/

# Manually modify the project file to add our build directory to search paths
echo "Modifying project file to include our custom library path..."
grep -q "build/Debug-iphonesimulator" "$PROJECT_FILE" || sed -i '' '/LIBRARY_SEARCH_PATHS/s/= (/= (\n\t\t\t\t\t"$(SRCROOT)\/build\/Debug-iphonesimulator",/' "$PROJECT_FILE"

echo "Xcode project file updated successfully."
