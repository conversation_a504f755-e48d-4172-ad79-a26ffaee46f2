#!/bin/bash

# Create the directory structure
mkdir -p "${PODS_ROOT}/Headers/Public/RNAudioRecorderPlayer"

# Copy the module map file
cp "${SRCROOT}/RNAudioRecorderPlayer.modulemap" "${PODS_ROOT}/Headers/Public/RNAudioRecorderPlayer/module.modulemap"

# Copy the header file
cp "${SRCROOT}/../node_modules/react-native-audio-recorder-player/ios/RNAudioRecorderPlayer.h" "${PODS_ROOT}/Headers/Public/RNAudioRecorderPlayer/"

echo "Module map fixed successfully!"
