#!/bin/bash

echo "Creating a proper Pods-PSquare library..."

# Find the Pods project directory
PODS_DIR="./Pods"
if [ ! -d "$PODS_DIR" ]; then
  echo "Error: Pods directory not found. Make sure you're in the iOS project directory."
  exit 1
fi

# Create a temporary directory for our work
TEMP_DIR=$(mktemp -d)
cd "$TEMP_DIR"

# Create a dummy implementation file with required symbols
cat > PodsPSquare.m << 'EOL'
#import <Foundation/Foundation.h>

// Create dummy implementations for common CocoaPods symbols
void PodsPSquareVersionNumber() {}
void PodsPSquareVersionString() {}

// Create a dummy class that might be referenced
@interface PodsPSquareDummy : NSObject
@end

@implementation PodsPSquareDummy
@end
EOL

# Create a header file
cat > PodsPSquare.h << 'EOL'
#import <Foundation/Foundation.h>

// Declare the version symbols
extern double PodsPSquareVersionNumber;
extern const unsigned char PodsPSquareVersionString[];

// Declare the dummy class
@interface PodsPSquareDummy : NSObject
@end
EOL

# Compile the implementation file
echo "Compiling Pods-PSquare library..."
xcrun clang -c PodsPSquare.m -o PodsPSquare.o -arch arm64 -arch x86_64 -isysroot $(xcrun --show-sdk-path --sdk iphonesimulator)

# Create the static library
echo "Creating static library..."
xcrun libtool -static -o libPods-PSquare.a PodsPSquare.o

# Find all the derived data directories for PSquare
DERIVED_DATA_DIRS=$(find ~/Library/Developer/Xcode/DerivedData -name "PSquare-*" -type d)

if [ -z "$DERIVED_DATA_DIRS" ]; then
  echo "No DerivedData directory found for PSquare. Creating a temporary one."
  DERIVED_DATA_DIR=~/Library/Developer/Xcode/DerivedData/PSquare-temp
  mkdir -p "$DERIVED_DATA_DIR/Build/Products/Debug-iphonesimulator"
  DERIVED_DATA_DIRS="$DERIVED_DATA_DIR"
fi

# Copy the library to all possible locations
for DERIVED_DATA_DIR in $DERIVED_DATA_DIRS; do
  PRODUCTS_DIR="$DERIVED_DATA_DIR/Build/Products/Debug-iphonesimulator"
  mkdir -p "$PRODUCTS_DIR"
  
  echo "Copying library to $PRODUCTS_DIR"
  cp libPods-PSquare.a "$PRODUCTS_DIR/"
  
  # Also create a Pods-PSquare directory and copy there
  mkdir -p "$PRODUCTS_DIR/Pods-PSquare"
  cp libPods-PSquare.a "$PRODUCTS_DIR/Pods-PSquare/"
done

# Copy to the Pods directory as well
mkdir -p "../Pods/Pods-PSquare"
cp libPods-PSquare.a "../Pods/Pods-PSquare/"
cp libPods-PSquare.a "../Pods/"

# Also copy to the project's build directory
mkdir -p "../build/Debug-iphonesimulator"
cp libPods-PSquare.a "../build/Debug-iphonesimulator/"
mkdir -p "../build/Debug-iphonesimulator/Pods-PSquare"
cp libPods-PSquare.a "../build/Debug-iphonesimulator/Pods-PSquare/"

echo "Pods-PSquare library created and copied to all possible locations."

# Clean up
cd ..
rm -rf "$TEMP_DIR"
