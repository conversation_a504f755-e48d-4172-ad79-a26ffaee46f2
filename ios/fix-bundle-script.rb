#!/usr/bin/env ruby

require 'xcodeproj'

puts "Fixing bundle script in Xcode project..."

# Open the project
project_path = 'PSquare.xcodeproj'
project = Xcodeproj::Project.open(project_path)

# Find the main target
main_target = project.targets.find { |t| t.name == 'PSquare' }

if main_target.nil?
  puts "Error: Could not find PSquare target"
  exit 1
end

puts "Found PSquare target, modifying bundle script..."

# Find the bundle script phase
bundle_script_phase = main_target.build_phases.find do |phase|
  phase.is_a?(Xcodeproj::Project::Object::PBXShellScriptBuildPhase) && 
  phase.name && phase.name.include?('Bundle React Native code and images')
end

if bundle_script_phase
  puts "Found bundle script phase, modifying..."
  
  # Add output files to the script phase
  bundle_script_phase.output_paths = [
    "$(DERIVED_FILE_DIR)/react-native-bundle-output"
  ]
  
  # Modify the script to create the output file
  original_script = bundle_script_phase.shell_script
  modified_script = original_script + "\n\n# Create output file to satisfy dependency analysis\ntouch \"${DERIVED_FILE_DIR}/react-native-bundle-output\""
  bundle_script_phase.shell_script = modified_script
  
  puts "Bundle script phase modified successfully"
else
  puts "Warning: Could not find bundle script phase"
end

# Save the project
project.save

puts "Xcode project updated successfully."

# Now let's also modify the Pods project to fix the React-Fabric and React-FabricComponents script phases
pods_project_path = 'Pods/Pods.xcodeproj'
if File.exist?(pods_project_path)
  puts "Modifying Pods project..."
  
  pods_project = Xcodeproj::Project.open(pods_project_path)
  
  # Find the React-Fabric and React-FabricComponents targets
  fabric_targets = pods_project.targets.select { |t| t.name == 'React-Fabric' || t.name == 'React-FabricComponents' }
  
  fabric_targets.each do |target|
    puts "Found #{target.name} target, modifying script phases..."
    
    # Find the check script phases
    check_script_phases = target.build_phases.select do |phase|
      phase.is_a?(Xcodeproj::Project::Object::PBXShellScriptBuildPhase) && 
      phase.name && phase.name.include?('[RN]Check rncore')
    end
    
    check_script_phases.each do |phase|
      puts "Found check script phase in #{target.name}, modifying..."
      
      # Add output files to the script phase
      phase.output_paths = [
        "$(DERIVED_FILE_DIR)/rncore-check-output"
      ]
      
      # Modify the script to create the output file
      original_script = phase.shell_script
      modified_script = original_script + "\n\n# Create output file to satisfy dependency analysis\ntouch \"${DERIVED_FILE_DIR}/rncore-check-output\""
      phase.shell_script = modified_script
      
      puts "Check script phase modified successfully in #{target.name}"
    end
  end
  
  # Save the Pods project
  pods_project.save
  
  puts "Pods project updated successfully."
end

puts "All modifications complete. Try building the project now."
