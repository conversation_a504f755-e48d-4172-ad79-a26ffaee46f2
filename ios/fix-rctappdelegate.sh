#!/bin/bash

echo "Creating dummy RCTAppDelegate.h file..."

# Create the directory if it doesn't exist
mkdir -p ./Pods/Headers/Public/React

# Create the dummy header file
cat > ./Pods/Headers/Public/React/RCTAppDelegate.h << 'EOL'
// Dummy header file for RCTAppDelegate
#ifndef RCTAppDelegate_h
#define RCTAppDelegate_h

#import <UIKit/UIKit.h>

@interface RCTAppDelegate : UIResponder <UIApplicationDelegate>

@property (nonatomic, strong) UIWindow *window;

@end

#endif /* RCTAppDelegate_h */
EOL

echo "Dummy RCTAppDelegate.h created successfully."
