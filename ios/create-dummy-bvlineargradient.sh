#!/bin/bash

# Create a temporary directory for our work
mkdir -p /tmp/bvlineargradient-fix
cd /tmp/bvlineargradient-fix

# Create a dummy implementation file
echo "#import <Foundation/Foundation.h>" > dummy.m
echo "@interface BVLinearGradient : NSObject" >> dummy.m
echo "@end" >> dummy.m
echo "@implementation BVLinearGradient" >> dummy.m
echo "@end" >> dummy.m

# Compile and create the static library
xcrun clang -c dummy.m -o dummy.o
xcrun ar rcs libBVLinearGradient.a dummy.o

# Find the derived data directory
DERIVED_DATA_DIR=$(find ~/Library/Developer/Xcode/DerivedData -name "PSquare-*" -type d | head -n 1)
if [ -z "$DERIVED_DATA_DIR" ]; then
  # If no directory exists yet, create one
  DERIVED_DATA_DIR=~/Library/Developer/Xcode/DerivedData/PSquare-temp
  mkdir -p "$DERIVED_DATA_DIR"
fi

# Create the products directory
mkdir -p "$DERIVED_DATA_DIR/Build/Products/Debug-iphonesimulator/BVLinearGradient"

# Copy the library to the derived data directory
cp libBVLinearGradient.a "$DERIVED_DATA_DIR/Build/Products/Debug-iphonesimulator/BVLinearGradient/"

echo "Dummy BVLinearGradient library created at $DERIVED_DATA_DIR/Build/Products/Debug-iphonesimulator/BVLinearGradient/libBVLinearGradient.a"

# Clean up
cd -
rm -rf /tmp/bvlineargradient-fix
