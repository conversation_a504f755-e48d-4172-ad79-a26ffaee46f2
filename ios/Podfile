require_relative '../node_modules/react-native/scripts/react_native_pods'
require_relative '../node_modules/@react-native-community/cli-platform-ios/native_modules'

platform :ios, '15.1'
prepare_react_native_project!

# Enable modular headers for Swift compatibility
use_modular_headers!

linkage = ENV['USE_FRAMEWORKS']
if linkage != nil
  Pod::UI.puts "Configuring Pod with #{linkage}ally linked Frameworks".green
  use_frameworks! :linkage => linkage.to_sym
end

target 'PSquare' do
  config = use_native_modules!

  use_react_native!(
    :path => config[:reactNativePath],
    # Disable Hermes temporarily to fix build issues
    :hermes_enabled => false,
    # Enable Fabric for React Native 0.76.6
    :fabric_enabled => true
  )

  target 'PSquareTests' do
    inherit! :complete
    # Pods for testing
  end

  post_install do |installer|
    # React Native post install
    react_native_post_install(
      installer,
      config[:reactNativePath],
      :mac_catalyst_enabled => false
    )

    installer.pods_project.targets.each do |target|
      target.build_configurations.each do |config|
        config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '15.1'
        config.build_settings['BUILD_LIBRARY_FOR_DISTRIBUTION'] = 'YES'

        # Fix for Swift version issues
        if target.name.include?("Firebase") || target.name.include?("GoogleUtilities")
          config.build_settings['SWIFT_VERSION'] = '5.0'
        end

        # Fix for react-native-maps
        if target.name == 'react-native-maps'
          config.build_settings['CLANG_ENABLE_MODULES'] = 'No'
        end
      end
    end
  end
end
