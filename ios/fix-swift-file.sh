#!/bin/bash

# This script replaces the problematic Swift file with a fixed version

# Find the RNAudioRecorderPlayer.swift file
SWIFT_FILE="../node_modules/react-native-audio-recorder-player/ios/RNAudioRecorderPlayer.swift"

if [ -f "$SWIFT_FILE" ]; then
  # Create a backup
  cp "$SWIFT_FILE" "${SWIFT_FILE}.bak"
  
  # Replace the file with our patched version
  cp "RNAudioRecorderPlayer.swift.patch" "$SWIFT_FILE"
  
  echo "Fixed RNAudioRecorderPlayer.swift file"
else
  echo "RNAudioRecorderPlayer.swift file not found"
fi
