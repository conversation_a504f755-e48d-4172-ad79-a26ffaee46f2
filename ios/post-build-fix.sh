#!/bin/bash

# This script is meant to be run after the build to fix the module map issue

# Find the build directory
BUILD_DIR="$HOME/Library/Developer/Xcode/DerivedData"
PSQUARE_BUILD_DIR=$(find "$BUILD_DIR" -name "<PERSON><PERSON><PERSON>-*" -type d | head -n 1)

if [ -z "$PSQUARE_BUILD_DIR" ]; then
  echo "Could not find PSquare build directory"
  exit 1
fi

echo "Found build directory: $PSQUARE_BUILD_DIR"

# Create a dummy module map file for RNAudioRecorderPlayer
MODULEMAP_DIR="$PSQUARE_BUILD_DIR/Build/Products/Debug-iphonesimulator/RNAudioRecorderPlayer"
mkdir -p "$MODULEMAP_DIR"

# Create the module map file
cat > "$MODULEMAP_DIR/RNAudioRecorderPlayer.modulemap" << EOF
module RNAudioRecorderPlayer {
  header "RNAudioRecorderPlayer.h"
  export *
}
EOF

# Copy the header file
cp "../node_modules/react-native-audio-recorder-player/ios/RNAudioRecorderPlayer.h" "$MODULEMAP_DIR/"

echo "Module map fixed successfully!"
