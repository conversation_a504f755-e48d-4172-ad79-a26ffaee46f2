#!/usr/bin/env ruby

require 'xcodeproj'

puts "Fixing linker settings in Xcode project..."

# Open the project
project_path = 'PSquare.xcodeproj'
project = Xcodeproj::Project.open(project_path)

# Find the main target
main_target = project.targets.find { |t| t.name == 'PSquare' }

if main_target.nil?
  puts "Error: Could not find PSquare target"
  exit 1
end

puts "Found PSquare target, modifying build settings..."

# Modify build settings for all configurations
main_target.build_configurations.each do |config|
  # Remove the problematic library search paths
  config.build_settings['LIBRARY_SEARCH_PATHS'] = [
    '$(inherited)',
    '"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)"',
    '"$(TOOLCHAIN_DIR)/usr/lib/swift-5.0/$(PLATFORM_NAME)"',
    '"$(SDKROOT)/usr/lib/swift"'
  ]
  
  # Add the build directory to the library search paths
  config.build_settings['LIBRARY_SEARCH_PATHS'] << '"$(SRCROOT)/build/Debug-iphonesimulator"'
  
  # Remove any references to Pods-PSquare from the linker flags
  if config.build_settings['OTHER_LDFLAGS']
    config.build_settings['OTHER_LDFLAGS'] = config.build_settings['OTHER_LDFLAGS'].reject { |flag| flag.include?('Pods-PSquare') }
  end
  
  # Add basic linker flags if they're missing
  if config.build_settings['OTHER_LDFLAGS'].nil? || config.build_settings['OTHER_LDFLAGS'].empty?
    config.build_settings['OTHER_LDFLAGS'] = [
      '$(inherited)',
      '-ObjC'
    ]
  end
  
  # Ensure we're using the legacy build system which is more forgiving
  config.build_settings['SWIFT_VERSION'] = '5.0'
  config.build_settings['ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES'] = 'YES'
  
  # Disable bitcode which can cause issues
  config.build_settings['ENABLE_BITCODE'] = 'NO'
  
  # Set deployment target
  config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '15.1'
  
  puts "Updated build settings for #{config.name} configuration"
end

# Save the project
project.save

puts "Xcode project updated successfully."

# Now let's also modify the Pods project
pods_project_path = 'Pods/Pods.xcodeproj'
if File.exist?(pods_project_path)
  puts "Modifying Pods project..."
  
  pods_project = Xcodeproj::Project.open(pods_project_path)
  
  # Find the Pods-PSquare target
  pods_target = pods_project.targets.find { |t| t.name == 'Pods-PSquare' }
  
  if pods_target
    puts "Found Pods-PSquare target, modifying build settings..."
    
    # Modify build settings for all configurations
    pods_target.build_configurations.each do |config|
      # Simplify the build settings
      config.build_settings['LIBRARY_SEARCH_PATHS'] = ['$(inherited)']
      config.build_settings['HEADER_SEARCH_PATHS'] = ['$(inherited)']
      config.build_settings['OTHER_LDFLAGS'] = ['$(inherited)', '-ObjC']
      
      puts "Updated build settings for #{config.name} configuration in Pods-PSquare target"
    end
  else
    puts "Warning: Could not find Pods-PSquare target in Pods project"
  end
  
  # Save the Pods project
  pods_project.save
  
  puts "Pods project updated successfully."
end

puts "All modifications complete. Try building the project now."
