#!/bin/bash

# This script fixes the PSquare_vers.c file to avoid module map issues

# Find the PSquare_vers.c file
VERS_FILE="${BUILT_PRODUCTS_DIR}/PSquare_vers.c"

if [ -f "$VERS_FILE" ]; then
  # Create a backup
  cp "$VERS_FILE" "${VERS_FILE}.bak"
  
  # Remove any imports that might cause module map issues
  sed -i '' '/RNAudioRecorderPlayer/d' "$VERS_FILE"
  
  echo "Fixed PSquare_vers.c file"
else
  echo "PSquare_vers.c file not found"
fi
