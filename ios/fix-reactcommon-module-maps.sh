#!/bin/bash

# Remove all module maps from ReactCommon and react_runtime
find ./Pods/Headers/Public/ReactCommon -name "*.modulemap" -delete 2>/dev/null
find ./Pods/Headers/Public/react_runtime -name "*.modulemap" -delete 2>/dev/null

# Create new module maps with different names
mkdir -p ./Pods/Headers/Public/ReactCommon
cat > ./Pods/Headers/Public/ReactCommon/ReactCommon.modulemap << EOL
module ReactCommonModule {
  umbrella header "ReactCommon-umbrella.h"
  export *
  module * { export * }
}
EOL

cat > ./Pods/Headers/Public/ReactCommon/React-RuntimeApple.modulemap << EOL
module ReactRuntimeAppleModule {
  umbrella header "React-RuntimeApple-umbrella.h"
  export *
  module * { export * }
}
EOL

echo "ReactCommon module maps fixed!"
