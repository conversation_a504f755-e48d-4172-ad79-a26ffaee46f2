{"name": "PSquare", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@ptomasroos/react-native-multi-slider": "^2.2.2", "@react-native-async-storage/async-storage": "^2.1.2", "@react-native-clipboard/clipboard": "^1 .14.2", "@react-native-community/datetimepicker": "^7.7.0", "@react-native-community/geolocation": "^3.4.0", "@react-native-community/slider": "^4.5.4", "@react-native-firebase/app": "^22.1.0", "@react-native-firebase/messaging": "^22.1.0", "@react-native-masked-view/masked-view": "^0.2.9", "@react-native-picker/picker": "^2.8.1", "@react-native/gradle-plugin": "^0.79.1", "@react-navigation/bottom-tabs": "^6.6.1", "@react-navigation/drawer": "^6.7.2", "@react-navigation/native": "^6.1.18", "@react-navigation/stack": "^6.4.1", "@reduxjs/toolkit": "^1.9.7", "@twotalltotems/react-native-otp-input": "^1.3.11", "axios": "^1.7.7", "core-js": "^3.38.1", "formik": "^2.4.6", "lottie-ios": "^4.5.0", "lottie-react-native": "^7.2.2", "moment": "^2.30.1", "react": "18.3.1", "react-native": "0.76.6", "react-native-audio-recorder-player": "^3.6.12", "react-native-big-calendar": "^4.15.1", "react-native-blob-util": "^0.19.11", "react-native-calendars": "^1.1307.0", "react-native-camera": "^4.2.1", "react-native-circular-progress": "^1.4.0", "react-native-compressor": "^1.10.3", "react-native-document-picker": "^9.3.1", "react-native-easy-content-loader": "^0.3.2", "react-native-element-dropdown": "^2.12.2", "react-native-fast-image": "^8.6.3", "react-native-file-viewer": "^2.1.5", "react-native-fs": "^2.20.0", "react-native-geocoding": "^0.5.0", "react-native-gesture-handler": "^2.21.2", "react-native-gradle-plugin": "^0.71.19", "react-native-html-to-pdf": "^0.12.0", "react-native-image-view": "^2.1.9", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-linear-gradient": "^2.8.3", "react-native-maps": "^1.18.2", "react-native-modal-dropdown": "^1.0.2", "react-native-pager-view": "^6.4.1", "react-native-paper": "^5.12.5", "react-native-permissions": "^4.1.5", "react-native-pie-chart": "^4.0.1", "react-native-qrcode-scanner": "^1.5.5", "react-native-qrcode-svg": "^6.3.12", "react-native-radio-buttons-group": "^3.1.0", "react-native-raw-bottom-sheet": "^2.2.1", "react-native-reanimated": "^3.16.6", "react-native-safe-area-context": "^4.14.1", "react-native-screens": "^3.35.0", "react-native-share": "^10.2.1", "react-native-sound": "^0.11.2", "react-native-splash-screen": "^3.3.0", "react-native-svg": "^15.11.2", "react-native-tab-view": "^3.5.2", "react-native-toast-message": "^2.2.1", "react-native-vector-icons": "^10.2.0", "react-native-video": "^6.5.0", "react-native-virtualized-view": "^1.0.0", "react-redux": "^8.1.3", "redux-persist": "^6.0.0", "redux-thunk": "^2.4.2", "rn-range-slider": "^2.2.2", "stream-chat-react-native": "^5.39.3", "yup": "^0.32.11"}, "devDependencies": {"@babel/core": "^7.26.0", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.26.0", "@react-native-community/cli": "15.0.1", "@react-native-community/cli-platform-android": "15.0.1", "@react-native-community/cli-platform-ios": "15.0.1", "@react-native/babel-preset": "0.76.6", "@react-native/eslint-config": "0.76.6", "@react-native/metro-config": "0.76.6", "@react-native/typescript-config": "0.76.6", "@tsconfig/react-native": "^3.0.5", "@types/react": "^18.3.14", "@types/react-native": "^0.72.8", "@types/react-redux": "^7.1.34", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.6.3", "babel-plugin-module-resolver": "^5.0.2", "eslint": "^8.19.0", "jest": "^29.6.3", "metro-react-native-babel-preset": "^0.77.0", "prettier": "^2.8.8", "react-native-svg-transformer": "^1.5.0", "react-test-renderer": "18.3.1", "typescript": "^5.0.4"}, "engines": {"node": ">=18"}}